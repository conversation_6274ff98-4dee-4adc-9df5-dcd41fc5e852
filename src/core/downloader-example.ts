import { Downloader, DownloadOptions } from './downloader'
import path from 'path'

/**
 * Example usage of the optimized Downloader class
 */
async function downloadExample() {
	const downloadOptions: DownloadOptions = {
		package_name: 'example-file.zip',
		cache_path: path.join(__dirname, '../../downloads'),
		url: 'https://httpbin.org/bytes/1024' // Example URL that returns 1KB of data
	}

	const downloader = new Downloader(downloadOptions)

	// Listen for progress events
	downloader.on('progress', (progress) => {
		console.log(`Download progress: ${(progress.percent * 100).toFixed(2)}% (${progress.transferred}/${progress.total} bytes)`)
	})

	// Listen for completion
	downloader.on('complete', (result) => {
		console.log(`Download completed! File saved to: ${result.path}`)
	})

	// Listen for errors
	downloader.on('error', (error) => {
		console.error('Download failed:', error.message)
	})

	try {
		console.log('Starting download...')
		await downloader.start()
		console.log('Download finished successfully!')
	} catch (error) {
		console.error('Download error:', error)
	}
}

// Example of canceling a download
async function downloadWithCancelExample() {
	const downloadOptions: DownloadOptions = {
		package_name: 'large-file.zip',
		cache_path: path.join(__dirname, '../../downloads'),
		url: 'https://httpbin.org/bytes/10485760' // 10MB file
	}

	const downloader = new Downloader(downloadOptions)

	downloader.on('progress', (progress) => {
		console.log(`Download progress: ${(progress.percent * 100).toFixed(2)}%`)
		
		// Cancel download after 50% progress
		if (progress.percent > 0.5) {
			console.log('Canceling download...')
			downloader.cancel()
		}
	})

	downloader.on('error', (error) => {
		if (error.name === 'CancelError') {
			console.log('Download was canceled')
		} else {
			console.error('Download failed:', error.message)
		}
	})

	try {
		await downloader.start()
	} catch (error) {
		console.log('Download was interrupted')
	}
}

// Run examples
if (require.main === module) {
	console.log('=== Basic Download Example ===')
	downloadExample()
		.then(() => {
			console.log('\n=== Download with Cancel Example ===')
			return downloadWithCancelExample()
		})
		.catch(console.error)
}

export { downloadExample, downloadWithCancelExample }
