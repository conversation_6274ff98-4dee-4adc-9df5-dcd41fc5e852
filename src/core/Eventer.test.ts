import { describe, it, expect, vi, beforeEach } from 'vitest'
import { register, trigger } from './Eventer'

describe('Eventer', () => {
  beforeEach(() => {
    // 清理所有事件监听器
    vi.clearAllMocks()
  })

  describe('register', () => {
    it('should register an event handler', () => {
      const handler = vi.fn()
      const registration = register('test-event', handler)

      expect(registration).toHaveProperty('unregister')
      expect(typeof registration.unregister).toBe('function')
    })

    it('should call handler when event is triggered', () => {
      const handler = vi.fn()
      register('test-event', handler)

      trigger('test-event', 'test-message')

      expect(handler).toHaveBeenCalledWith('test-message')
      expect(handler).toHaveBeenCalledTimes(1)
    })

    it('should handle multiple handlers for the same event', () => {
      const handler1 = vi.fn()
      const handler2 = vi.fn()
      
      register('test-event', handler1)
      register('test-event', handler2)

      trigger('test-event', 'test-message')

      expect(handler1).toHaveBeenCalledWith('test-message')
      expect(handler2).toHaveBeenCalledWith('test-message')
    })

    it('should unregister event handler', () => {
      const handler = vi.fn()
      const registration = register('test-event', handler)

      // 触发事件，应该被调用
      trigger('test-event', 'test-message')
      expect(handler).toHaveBeenCalledTimes(1)

      // 取消注册
      registration.unregister()

      // 再次触发事件，不应该被调用
      trigger('test-event', 'test-message-2')
      expect(handler).toHaveBeenCalledTimes(1) // 仍然是1次
    })
  })

  describe('trigger', () => {
    it('should trigger event with message', () => {
      const handler = vi.fn()
      register('test-event', handler)

      const testMessage = { data: 'test' }
      trigger('test-event', testMessage)

      expect(handler).toHaveBeenCalledWith(testMessage)
    })

    it('should not throw error when triggering non-existent event', () => {
      expect(() => {
        trigger('non-existent-event', 'message')
      }).not.toThrow()
    })

    it('should handle null/undefined messages', () => {
      const handler = vi.fn()
      register('test-event', handler)

      trigger('test-event', null)
      expect(handler).toHaveBeenCalledWith(null)

      trigger('test-event', undefined)
      expect(handler).toHaveBeenCalledWith(undefined)
    })
  })

  describe('multiple events', () => {
    it('should handle different events independently', () => {
      const handler1 = vi.fn()
      const handler2 = vi.fn()

      register('event-1', handler1)
      register('event-2', handler2)

      trigger('event-1', 'message-1')
      expect(handler1).toHaveBeenCalledWith('message-1')
      expect(handler2).not.toHaveBeenCalled()

      trigger('event-2', 'message-2')
      expect(handler2).toHaveBeenCalledWith('message-2')
      expect(handler1).toHaveBeenCalledTimes(1) // 仍然只被调用一次
    })
  })
})
