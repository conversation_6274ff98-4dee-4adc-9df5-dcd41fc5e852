/**
 * 测试工具函数
 * 提供常用的测试辅助功能
 */

import { vi } from 'vitest'

/**
 * 创建模拟的 IPC 事件对象
 */
export function createMockIpcEvent(returnValue?: any) {
  return {
    reply: vi.fn(),
    returnValue,
    sender: {
      send: vi.fn()
    }
  }
}

/**
 * 创建模拟的 BrowserWindow 实例
 */
export function createMockBrowserWindow(options: any = {}) {
  return {
    id: Math.random(),
    webContents: {
      id: Math.random(),
      send: vi.fn(),
      setWindowOpenHandler: vi.fn(),
      ...options.webContents
    },
    loadURL: vi.fn(),
    loadFile: vi.fn(),
    show: vi.fn(),
    hide: vi.fn(),
    close: vi.fn(),
    destroy: vi.fn(),
    on: vi.fn(),
    once: vi.fn(),
    removeAllListeners: vi.fn(),
    isDestroyed: vi.fn().mockReturnValue(false),
    ...options
  }
}

/**
 * 等待指定时间
 */
export function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms))
}

/**
 * 创建模拟的文件路径
 */
export function createMockPath(...segments: string[]): string {
  return segments.join('/')
}

/**
 * 模拟异步操作
 */
export function mockAsyncOperation<T>(
  result: T,
  delay: number = 0
): Promise<T> {
  return new Promise(resolve => {
    setTimeout(() => resolve(result), delay)
  })
}

/**
 * 创建模拟的错误对象
 */
export function createMockError(message: string, code?: string): Error {
  const error = new Error(message)
  if (code) {
    ;(error as any).code = code
  }
  return error
}

/**
 * 验证函数是否被调用指定次数
 */
export function expectCalledTimes(mockFn: any, times: number) {
  expect(mockFn).toHaveBeenCalledTimes(times)
}

/**
 * 验证函数是否被调用并传入指定参数
 */
export function expectCalledWith(mockFn: any, ...args: any[]) {
  expect(mockFn).toHaveBeenCalledWith(...args)
}

/**
 * 重置所有模拟函数
 */
export function resetAllMocks() {
  vi.clearAllMocks()
}

/**
 * 模拟 Electron 应用事件
 */
export function mockElectronAppEvent(eventName: string, callback?: Function) {
  const mockApp = vi.mocked(require('electron').app)
  if (callback) {
    mockApp.on.mockImplementation((event, cb) => {
      if (event === eventName) {
        cb()
      }
    })
  }
  return mockApp
}

/**
 * 模拟文件系统操作
 */
export function mockFileSystem() {
  return {
    readFile: vi.fn(),
    writeFile: vi.fn(),
    exists: vi.fn(),
    mkdir: vi.fn(),
    rmdir: vi.fn()
  }
}
