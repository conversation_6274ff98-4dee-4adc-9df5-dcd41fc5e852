/**
 * Electron API 模拟
 * 提供完整的 Electron API 模拟实现
 */

import { vi } from 'vitest'

/**
 * 模拟 Electron 主进程 API
 */
export const mockElectronMain = {
  app: {
    whenReady: vi.fn().mockResolvedValue(undefined),
    on: vi.fn(),
    once: vi.fn(),
    quit: vi.fn(),
    exit: vi.fn(),
    getPath: vi.fn().mockReturnValue('/mock/path'),
    getName: vi.fn().mockReturnValue('TestApp'),
    getVersion: vi.fn().mockReturnValue('1.0.0'),
    setAppUserModelId: vi.fn(),
    requestSingleInstanceLock: vi.fn().mockReturnValue(true),
    hasSingleInstanceLock: vi.fn().mockReturnValue(true),
    releaseSingleInstanceLock: vi.fn()
  },

  BrowserWindow: vi.fn().mockImplementation((options = {}) => ({
    id: Math.random(),
    webContents: {
      id: Math.random(),
      send: vi.fn(),
      setWindowOpenHandler: vi.fn(),
      openDevTools: vi.fn(),
      closeDevTools: vi.fn(),
      isDevToolsOpened: vi.fn().mockReturnValue(false),
      session: {
        clearStorageData: vi.fn()
      }
    },
    loadURL: vi.fn().mockResolvedValue(undefined),
    loadFile: vi.fn().mockResolvedValue(undefined),
    show: vi.fn(),
    hide: vi.fn(),
    close: vi.fn(),
    destroy: vi.fn(),
    minimize: vi.fn(),
    maximize: vi.fn(),
    unmaximize: vi.fn(),
    isMaximized: vi.fn().mockReturnValue(false),
    setFullScreen: vi.fn(),
    isFullScreen: vi.fn().mockReturnValue(false),
    on: vi.fn(),
    once: vi.fn(),
    removeAllListeners: vi.fn(),
    isDestroyed: vi.fn().mockReturnValue(false),
    getBounds: vi.fn().mockReturnValue({ x: 0, y: 0, width: 800, height: 600 }),
    setBounds: vi.fn(),
    getSize: vi.fn().mockReturnValue([800, 600]),
    setSize: vi.fn(),
    ...options
  })),

  ipcMain: {
    on: vi.fn(),
    once: vi.fn(),
    handle: vi.fn(),
    removeHandler: vi.fn(),
    removeAllListeners: vi.fn()
  },

  shell: {
    openExternal: vi.fn().mockResolvedValue(undefined),
    openPath: vi.fn().mockResolvedValue(''),
    showItemInFolder: vi.fn(),
    moveItemToTrash: vi.fn().mockReturnValue(true)
  },

  dialog: {
    showOpenDialog: vi.fn().mockResolvedValue({ canceled: false, filePaths: [] }),
    showSaveDialog: vi.fn().mockResolvedValue({ canceled: false, filePath: '' }),
    showMessageBox: vi.fn().mockResolvedValue({ response: 0 }),
    showErrorBox: vi.fn()
  },

  Menu: {
    buildFromTemplate: vi.fn(),
    setApplicationMenu: vi.fn(),
    getApplicationMenu: vi.fn()
  },

  Tray: vi.fn().mockImplementation(() => ({
    setToolTip: vi.fn(),
    setContextMenu: vi.fn(),
    destroy: vi.fn()
  })),

  globalShortcut: {
    register: vi.fn().mockReturnValue(true),
    unregister: vi.fn(),
    unregisterAll: vi.fn(),
    isRegistered: vi.fn().mockReturnValue(false)
  },

  powerMonitor: {
    on: vi.fn(),
    getSystemIdleState: vi.fn().mockReturnValue('active'),
    getSystemIdleTime: vi.fn().mockReturnValue(0)
  }
}

/**
 * 模拟 Electron 渲染进程 API
 */
export const mockElectronRenderer = {
  ipcRenderer: {
    invoke: vi.fn().mockResolvedValue(undefined),
    on: vi.fn(),
    once: vi.fn(),
    removeAllListeners: vi.fn(),
    send: vi.fn(),
    sendSync: vi.fn()
  },

  webFrame: {
    setZoomFactor: vi.fn(),
    getZoomFactor: vi.fn().mockReturnValue(1),
    setZoomLevel: vi.fn(),
    getZoomLevel: vi.fn().mockReturnValue(0)
  },

  contextBridge: {
    exposeInMainWorld: vi.fn()
  }
}

/**
 * 获取完整的 Electron 模拟对象
 */
export function getElectronMock() {
  return {
    ...mockElectronMain,
    ...mockElectronRenderer
  }
}

/**
 * 重置所有 Electron 模拟
 */
export function resetElectronMocks() {
  Object.values(mockElectronMain).forEach(mock => {
    if (typeof mock === 'object' && mock !== null) {
      Object.values(mock).forEach(fn => {
        if (vi.isMockFunction(fn)) {
          fn.mockReset()
        }
      })
    } else if (vi.isMockFunction(mock)) {
      mock.mockReset()
    }
  })

  Object.values(mockElectronRenderer).forEach(mock => {
    if (typeof mock === 'object' && mock !== null) {
      Object.values(mock).forEach(fn => {
        if (vi.isMockFunction(fn)) {
          fn.mockReset()
        }
      })
    } else if (vi.isMockFunction(mock)) {
      mock.mockReset()
    }
  })
}
