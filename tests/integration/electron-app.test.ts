import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mockElectronMain } from '@tests/helpers/electron-mocks'

describe('Electron App Integration', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Application Lifecycle', () => {
    it('should handle app ready event', async () => {
      const { app } = mockElectronMain
      
      // 模拟 app.whenReady 被调用
      const readyPromise = Promise.resolve()
      app.whenReady.mockReturnValue(readyPromise)
      
      await readyPromise
      
      expect(app.whenReady).toHaveBeenCalled()
    })

    it('should handle app quit', () => {
      const { app } = mockElectronMain
      
      app.quit()
      
      expect(app.quit).toHaveBeenCalled()
    })

    it('should handle single instance lock', () => {
      const { app } = mockElectronMain
      
      const hasLock = app.requestSingleInstanceLock()
      
      expect(app.requestSingleInstanceLock).toHaveBeenCalled()
      expect(hasLock).toBe(true)
    })
  })

  describe('Window Management', () => {
    it('should create browser window with correct options', () => {
      const { BrowserWindow } = mockElectronMain
      
      const options = {
        width: 800,
        height: 600,
        webPreferences: {
          nodeIntegration: false,
          contextIsolation: true
        }
      }
      
      const window = new BrowserWindow(options)
      
      expect(BrowserWindow).toHaveBeenCalledWith(options)
      expect(window).toBeDefined()
    })

    it('should handle window events', () => {
      const { BrowserWindow } = mockElectronMain
      
      const window = new BrowserWindow()
      const eventHandler = vi.fn()
      
      window.on('ready-to-show', eventHandler)
      
      expect(window.on).toHaveBeenCalledWith('ready-to-show', eventHandler)
    })

    it('should load URL in window', async () => {
      const { BrowserWindow } = mockElectronMain
      
      const window = new BrowserWindow()
      const url = 'https://example.com'
      
      await window.loadURL(url)
      
      expect(window.loadURL).toHaveBeenCalledWith(url)
    })

    it('should load file in window', async () => {
      const { BrowserWindow } = mockElectronMain
      
      const window = new BrowserWindow()
      const filePath = '/path/to/index.html'
      
      await window.loadFile(filePath)
      
      expect(window.loadFile).toHaveBeenCalledWith(filePath)
    })
  })

  describe('IPC Communication', () => {
    it('should handle IPC main events', () => {
      const { ipcMain } = mockElectronMain
      
      const eventHandler = vi.fn()
      ipcMain.on('test-event', eventHandler)
      
      expect(ipcMain.on).toHaveBeenCalledWith('test-event', eventHandler)
    })

    it('should handle IPC main method calls', () => {
      const { ipcMain } = mockElectronMain
      
      const methodHandler = vi.fn().mockResolvedValue('test result')
      ipcMain.handle('test-method', methodHandler)
      
      expect(ipcMain.handle).toHaveBeenCalledWith('test-method', methodHandler)
    })

    it('should remove IPC handlers', () => {
      const { ipcMain } = mockElectronMain
      
      ipcMain.removeHandler('test-method')
      
      expect(ipcMain.removeHandler).toHaveBeenCalledWith('test-method')
    })
  })

  describe('System Integration', () => {
    it('should open external URLs', async () => {
      const { shell } = mockElectronMain
      
      const url = 'https://example.com'
      await shell.openExternal(url)
      
      expect(shell.openExternal).toHaveBeenCalledWith(url)
    })

    it('should show file dialogs', async () => {
      const { dialog } = mockElectronMain
      
      const options = {
        title: 'Select File',
        filters: [{ name: 'Text Files', extensions: ['txt'] }]
      }
      
      const result = await dialog.showOpenDialog(options)
      
      expect(dialog.showOpenDialog).toHaveBeenCalledWith(options)
      expect(result).toEqual({ canceled: false, filePaths: [] })
    })

    it('should show message boxes', async () => {
      const { dialog } = mockElectronMain
      
      const options = {
        type: 'info',
        title: 'Information',
        message: 'Test message'
      }
      
      const result = await dialog.showMessageBox(options)
      
      expect(dialog.showMessageBox).toHaveBeenCalledWith(options)
      expect(result).toEqual({ response: 0 })
    })
  })

  describe('Menu and Tray', () => {
    it('should create application menu', () => {
      const { Menu } = mockElectronMain
      
      const template = [
        {
          label: 'File',
          submenu: [
            { label: 'New', accelerator: 'CmdOrCtrl+N' }
          ]
        }
      ]
      
      Menu.buildFromTemplate(template)
      
      expect(Menu.buildFromTemplate).toHaveBeenCalledWith(template)
    })

    it('should create system tray', () => {
      const { Tray } = mockElectronMain
      
      const iconPath = '/path/to/icon.png'
      const tray = new Tray(iconPath)
      
      expect(Tray).toHaveBeenCalledWith(iconPath)
      expect(tray).toBeDefined()
    })
  })

  describe('Global Shortcuts', () => {
    it('should register global shortcuts', () => {
      const { globalShortcut } = mockElectronMain
      
      const accelerator = 'CommandOrControl+X'
      const callback = vi.fn()
      
      const success = globalShortcut.register(accelerator, callback)
      
      expect(globalShortcut.register).toHaveBeenCalledWith(accelerator, callback)
      expect(success).toBe(true)
    })

    it('should unregister global shortcuts', () => {
      const { globalShortcut } = mockElectronMain
      
      const accelerator = 'CommandOrControl+X'
      globalShortcut.unregister(accelerator)
      
      expect(globalShortcut.unregister).toHaveBeenCalledWith(accelerator)
    })
  })
})
